#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


from typing import List
import torch
from torch import nn
import math
from agent_ppo.conf.conf import Config

import sys
import os

if os.path.basename(sys.argv[0]) == "learner.py":
    import torch

    torch.set_num_interop_threads(2)
    torch.set_num_threads(2)
else:
    import torch

    torch.set_num_interop_threads(4)
    torch.set_num_threads(4)


class SelfAttentionBlock(nn.Module):
    """
    经典 Transformer Encoder block（去掉 position-wise bias，简化实现）
    """
    def __init__(self, d_model: int, n_head: int, dropout: float = 0.1):
        super().__init__()
        self.mha = nn.MultiheadAttention(d_model, n_head, dropout=dropout, batch_first=True, bias=False)
        self.norm1 = nn.LayerNorm(d_model)
        self.ffn  = nn.Sequential(
            nn.Linear(d_model, 4 * d_model),
            nn.ReLU(),
            nn.Linear(4 * d_model, d_model),
        )
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):                       # x: (B, L, d_model)
        attn_out, _ = self.mha(x, x, x)        # Self attention
        x = self.norm1(x + self.dropout(attn_out))
        ffn_out = self.dropout(self.ffn(x))           # 加 dropout
        x = self.norm2(x + ffn_out)
        return x                                # (B, L, d_model)


class CrossAttentionBlock(nn.Module):
    """
    交叉注意力模块：英雄状态作为query，环境物件作为key和value
    """
    def __init__(self, d_model: int, n_head: int, dropout: float = 0.1):
        super().__init__()
        self.cross_attn = nn.MultiheadAttention(d_model, n_head, dropout=dropout, batch_first=True, bias=False)
        self.norm1 = nn.LayerNorm(d_model)
        self.ffn = nn.Sequential(
            nn.Linear(d_model, 4 * d_model),
            nn.ReLU(),
            nn.Linear(4 * d_model, d_model),
        )
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)

    def forward(self, query, key_value):        # query: (B, 1, d_model), key_value: (B, 15, d_model)
        attn_out, attn_weights = self.cross_attn(query, key_value, key_value)  # Cross attention
        query = self.norm1(query + self.dropout(attn_out))
        ffn_out = self.dropout(self.ffn(query))
        query = self.norm2(query + ffn_out)
        return query, attn_weights               # (B, 1, d_model), (B, n_head, 1, 15)


class NetworkModelBase(nn.Module):
    def __init__(self):
        super().__init__()
        # feature configure parameter
        # 特征配置参数
        self.data_split_shape = Config.DATA_SPLIT_SHAPE
        self.feature_split_shape = Config.FEATURE_SPLIT_SHAPE
        self.label_size = Config.ACTION_NUM
        self.feature_len = Config.FEATURE_LEN
        self.value_num = Config.VALUE_NUM

        self.var_beta = Config.BETA_START
        self.vf_coef = Config.VF_COEF

        self.clip_param = Config.CLIP_PARAM

        self.data_len = Config.data_len

        # ---------- 特征分类和注意力相关超参 ----------
        self.d_model = 64                                   # 每个 token 的维度
        self.n_head = 4

        # 根据Config.FEATURES分析特征结构：
        # [2, 6, 128, 128, 15, 18, 6, 16, 121, 121, 121, 121, 121]
        # 英雄状态特征：前8个维度 (2+6)
        # 环境物件特征：第6个部分是simple_feats (18维，包含3个物件各6维)
        # 其他特征：地图信息等

        self.hero_feature_dim = 8  # 英雄状态特征维度 (2+6)
        self.object_feature_dim = 6  # 每个环境物件的特征维度
        self.num_objects = 15  # 环境物件数量 (1个BUFF + 13个宝箱 + 1个终点)

        # 英雄状态编码器 (query)
        self.hero_encoder = nn.Linear(self.hero_feature_dim, self.d_model)
        nn.init.xavier_uniform_(self.hero_encoder.weight)
        nn.init.zeros_(self.hero_encoder.bias)

        # 环境物件编码器 (key & value)
        self.object_encoder = nn.Linear(self.object_feature_dim, self.d_model)
        nn.init.xavier_uniform_(self.object_encoder.weight)
        nn.init.zeros_(self.object_encoder.bias)

        # 其他特征编码器 (计算剩余特征维度)
        # 根据FEATURES计算：[128, 128, 15, 6, 16, 121, 121, 121, 121, 121]
        other_parts = [128, 128, 15, 6, 16, 121, 121, 121, 121, 121]
        self.other_feature_dim = sum(other_parts)  # 应该是797
        self.other_encoder = nn.Linear(self.other_feature_dim, self.d_model * 2)
        nn.init.xavier_uniform_(self.other_encoder.weight)
        nn.init.zeros_(self.other_encoder.bias)

        # 交叉注意力模块
        self.cross_attention = CrossAttentionBlock(self.d_model, self.n_head)

        # 特征融合层
        self.feature_fusion = nn.Linear(self.d_model * 4, self.d_model * 2)  # hero + attended + other
        nn.init.xavier_uniform_(self.feature_fusion.weight)
        nn.init.zeros_(self.feature_fusion.bias)

        # 最终的MLP层
        self.final_dim = self.d_model * 2
        self.label_mlp = MLP([self.final_dim, 128, self.label_size], "label_mlp")
        self.value_mlp = MLP([self.final_dim, 128, self.value_num], "value_mlp")
    def process_legal_action(self, label, legal_action):
        label_max, _ = torch.max(label * legal_action, 1, True)
        label = label - label_max
        label = label * legal_action
        label = label + 1e5 * (legal_action - 1)
        return label

    def extract_features(self, feature):
        """
        从原始特征中提取英雄状态、环境物件和其他特征
        根据Config.FEATURES的结构进行特征分割
        """
        B = feature.size(0)

        # 根据Config.FEATURES分割特征
        # [2, 6, 128, 128, 15, 18, 6, 16, 121, 121, 121, 121, 121]
        split_features = torch.split(feature, self.feature_split_shape, dim=1)

        # 英雄状态特征：前两个部分 (2+6=8维)
        hero_features = torch.cat([split_features[0], split_features[1]], dim=1)  # (B, 8)

        # 环境物件特征：第6个部分是simple_feats (18维，包含3个物件各6维)
        simple_feats = split_features[5]  # (B, 18)
        object_features = simple_feats.view(B, 3, 6)  # (B, 3, 6)

        # 为了处理15个物件，我们需要扩展到15个物件
        # 这里我们复制现有的3个物件特征，并用零填充剩余的12个
        padding = torch.zeros(B, 12, 6, device=feature.device, dtype=feature.dtype)
        object_features = torch.cat([object_features, padding], dim=1)  # (B, 15, 6)

        # 其他特征：剩余的所有特征（除了英雄状态和simple_feats）
        other_features = torch.cat([
            split_features[2],  # 128维 (pos_row)
            split_features[3],  # 128维 (pos_col)
            split_features[4],  # 15维 (obj_status)
            split_features[6],  # 6维 (feature_history_pos)
            split_features[7],  # 16维 (legal_action)
            split_features[8],  # 121维 (treasure_flag)
            split_features[9],  # 121维 (end_flag)
            split_features[10], # 121维 (obstacle_flag)
            split_features[11], # 121维 (buff_flag)
            split_features[12], # 121维 (memory_flag)
        ], dim=1)  # (B, other_feature_dim)

        return hero_features, object_features, other_features

    def forward(self, feature, legal_action):
        B = feature.size(0)

        # ------------- 特征分类 -------------
        hero_features, object_features, other_features = self.extract_features(feature)

        # ------------- 特征编码 -------------
        # 英雄状态编码 (query)
        hero_encoded = self.hero_encoder(hero_features).unsqueeze(1)  # (B, 1, d_model)

        # 环境物件编码 (key & value)
        object_encoded = self.object_encoder(object_features)  # (B, 15, d_model)

        # 其他特征编码
        other_encoded = self.other_encoder(other_features)  # (B, d_model*2)

        # ------------- 交叉注意力 -------------
        attended_hero, attn_weights = self.cross_attention(hero_encoded, object_encoded)
        attended_hero = attended_hero.squeeze(1)  # (B, d_model)

        # ------------- 特征融合 -------------
        fused_features = torch.cat([
            hero_encoded.squeeze(1),  # (B, d_model)
            attended_hero,            # (B, d_model)
            other_encoded            # (B, d_model*2)
        ], dim=1)  # (B, d_model*4)

        final_features = self.feature_fusion(fused_features)  # (B, d_model*2)
        final_features = torch.relu(final_features)

        # ------------- 输出层 -------------
        logits = self.label_mlp(final_features)
        logits = self.process_legal_action(logits, legal_action)
        prob = torch.nn.functional.softmax(logits, dim=1)
        value = self.value_mlp(final_features)

        return prob, value


class NetworkModelActor(NetworkModelBase):
    def format_data(self, obs, legal_action, device=None):
        device = device or next(self.parameters()).device
        return (torch.tensor(obs, dtype=torch.float32, device=device),
                torch.tensor(legal_action, dtype=torch.float32, device=device))


class NetworkModelLearner(NetworkModelBase):
    def format_data(self, datas):
        return datas.view(-1, self.data_len).float().split(self.data_split_shape, dim=1)

    def forward(self, data_list, inference=False):
        feature = data_list[0]
        legal_action = data_list[-1]
        return super().forward(feature, legal_action)


def make_fc_layer(in_features: int, out_features: int):
    # Wrapper function to create and initialize a linear layer
    # 创建并初始化一个线性层
    fc_layer = nn.Linear(in_features, out_features)

    # initialize weight and bias
    # 初始化权重及偏移量
    nn.init.orthogonal_(fc_layer.weight)
    nn.init.zeros_(fc_layer.bias)

    return fc_layer


class MLP(nn.Module):
    def __init__(
        self,
        fc_feat_dim_list: List[int],
        name: str,
        non_linearity: nn.Module = nn.ReLU,
        non_linearity_last: bool = False,
    ):
        # Create a MLP object
        # 创建一个 MLP 对象
        super().__init__()
        self.fc_layers = nn.Sequential()
        for i in range(len(fc_feat_dim_list) - 1):
            fc_layer = make_fc_layer(fc_feat_dim_list[i], fc_feat_dim_list[i + 1])
            self.fc_layers.add_module("{0}_fc{1}".format(name, i + 1), fc_layer)
            # no relu for the last fc layer of the mlp unless required
            # 除非有需要，否则 mlp 的最后一个 fc 层不使用 relu
            if i + 1 < len(fc_feat_dim_list) - 1 or non_linearity_last:
                self.fc_layers.add_module("{0}_non_linear{1}".format(name, i + 1), non_linearity())

    def forward(self, data):
        return self.fc_layers(data)
